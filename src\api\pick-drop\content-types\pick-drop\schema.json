{"kind": "collectionType", "collectionName": "pick_drops", "info": {"singularName": "pick-drop", "pluralName": "pick-drops", "displayName": "pickDrop", "description": ""}, "options": {"draftAndPublish": false}, "attributes": {"senderName": {"type": "string"}, "senderPhoneNumber": {"type": "string"}, "receiverName": {"type": "string"}, "receiverPhoneNumber": {"type": "string"}, "itemDescription": {"type": "text"}, "itemWeightKg": {"type": "decimal"}, "itemImage": {"type": "media", "multiple": false, "required": false, "allowedTypes": ["images"]}, "pickupLocation": {"type": "string"}, "pickupDateTime": {"type": "datetime"}, "pickupAddress": {"type": "string"}, "dropOffLocation": {"type": "string"}, "dropOffDateTime": {"type": "datetime"}, "dropOffAddress": {"type": "string"}, "pickDropStatus": {"type": "enumeration", "enum": ["Pending", "Confirmed", "Cancelled", "In Transit", "Completed"]}, "assignedRider": {"type": "string"}, "users_permissions_user": {"type": "relation", "relation": "manyToOne", "target": "plugin::users-permissions.user", "inversedBy": "pick_drops"}, "deliveryType": {"type": "enumeration", "enum": ["Standard", "Same-Day", "Next-Day", "Scheduled", "Express"]}, "scheduledDateTime": {"type": "datetime"}, "price": {"type": "decimal"}, "subtotal": {"type": "decimal", "default": 0}, "deliveryFee": {"type": "decimal", "default": 0}, "totalAmount": {"type": "decimal", "default": 0}, "adminNotes": {"type": "text"}, "approvedAt": {"type": "datetime"}, "approvedBy": {"type": "relation", "relation": "manyToOne", "target": "plugin::users-permissions.user"}, "senderAddressLine1": {"type": "string"}, "senderAddressLine2": {"type": "string"}, "receiverAddressLine1": {"type": "string"}, "receiverAddressLine2": {"type": "string"}}}