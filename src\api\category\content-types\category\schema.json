{"kind": "collectionType", "collectionName": "categories", "info": {"singularName": "category", "pluralName": "categories", "displayName": "Category", "description": ""}, "options": {"draftAndPublish": false}, "attributes": {"name": {"type": "string"}, "slug": {"type": "string"}, "displayOrder": {"type": "integer"}, "products": {"type": "relation", "relation": "oneToMany", "target": "api::product.product", "mappedBy": "category"}, "image": {"allowedTypes": ["images", "files", "videos", "audios"], "type": "media", "multiple": false}}}