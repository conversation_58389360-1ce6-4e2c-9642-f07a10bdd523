{"kind": "collectionType", "collectionName": "wishlists", "info": {"singularName": "wishlist", "pluralName": "wishlists", "displayName": "wishlist"}, "options": {"draftAndPublish": false}, "attributes": {"users_permissions_user": {"type": "relation", "relation": "oneToOne", "target": "plugin::users-permissions.user", "inversedBy": "wishlist"}, "products": {"type": "relation", "relation": "oneToMany", "target": "api::product.product"}}}