{"kind": "collectionType", "collectionName": "payments", "info": {"singularName": "payment", "pluralName": "payments", "displayName": "Payment", "description": "Track payment information for orders"}, "options": {"draftAndPublish": false}, "attributes": {"amount": {"type": "decimal", "required": true}, "status": {"type": "enumeration", "enum": ["pending", "processing", "completed", "failed", "refunded"], "default": "pending", "required": true}, "paymentMethod": {"type": "enumeration", "enum": ["card", "paypal", "apple_pay", "google_pay", "cash_on_delivery", "bank_transfer"], "required": true}, "transactionId": {"type": "string"}, "paymentDetails": {"type": "json"}, "order": {"type": "relation", "relation": "oneToOne", "target": "api::order.order", "inversedBy": "payment"}, "users_permissions_user": {"type": "relation", "relation": "manyToOne", "target": "plugin::users-permissions.user", "inversedBy": "payments"}}}