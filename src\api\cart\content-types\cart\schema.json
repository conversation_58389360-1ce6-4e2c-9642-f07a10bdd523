{"kind": "collectionType", "collectionName": "carts", "info": {"singularName": "cart", "pluralName": "carts", "displayName": "<PERSON><PERSON>", "description": ""}, "options": {"draftAndPublish": false}, "attributes": {"item": {"displayName": "items", "type": "component", "repeatable": true, "component": "shared.items"}, "users_permissions_user": {"type": "relation", "relation": "oneToOne", "target": "plugin::users-permissions.user", "inversedBy": "cart", "unique": true}}}