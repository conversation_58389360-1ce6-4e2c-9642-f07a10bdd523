{"kind": "collectionType", "collectionName": "notifications", "info": {"singularName": "notification", "pluralName": "notifications", "displayName": "Notification"}, "options": {"draftAndPublish": false}, "attributes": {"title": {"type": "string"}, "message": {"type": "text"}, "read": {"type": "boolean"}, "users_permissions_user": {"type": "relation", "relation": "manyToOne", "target": "plugin::users-permissions.user", "inversedBy": "notifications"}}}